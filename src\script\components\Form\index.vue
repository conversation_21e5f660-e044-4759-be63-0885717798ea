<template>
    <el-form
        ref="form"
        v-if="show"
        v-bind="$attrs"
        :inline="inline"
        :model="form"
        :rules="rules"
        :label-width="labelWidth || 'auto'"
        :style="{ gap: `${gap}%` }"
        @submit.native.prevent
    >
        <slot name="prefix"></slot>

        <el-form-item
            v-for="(item, index) of formConfig"
            :label-width="labelWidth || item.labelWidth || 'auto'"
            :label="item.label"
            :prop="item.key"
            :key="index"
            :style="{ width: `${(item.span / 12) * 100 - gap}%` }"
        >
            <component
                v-if="item.componentId !== 1099"
                v-bind="{ ...item, value: form[item.key], size: item.size || 'small' }"
                v-model="form[item.key]"
                :ref="item.key"
                :is="COMPONENT_MAP[item.componentId]"
                :options="item.options"
                @click="click(item, $event)"
                @input="input(item, $event)"
                @change="change(item, $event)"
                @blur="blur(item, $event)"
                @mousedown="mousedown(item, $event)"
                @focusTarget="focusTarget(item, $event)"
                @remoteMethod="(value) => remoteMethod(item, value)"
            >
                <template v-for="slotName of item.slotNames" #[`${item.key}${slotName}`]>
                    <slot :name="`${item.key}${slotName}`"></slot>
                </template>
            </component>
            <slot v-else :name="item.key" :data="item"></slot>
        </el-form-item>
        <slot name="suffix"></slot>
    </el-form>
</template>

<script>
import _ from 'lodash';
import FormItem from './FormItem';

/**
 * Form组件 - 可配置的动态表单组件
 * @component
 */
export default {
    name: 'Form',
    props: {
        /**
         * 表单配置数组，用于动态生成表单项
         * @type {Array<Object>}
         * @property {string} key - 表单项的唯一标识
         * @property {string} label - 表单项的标签文本
         * @property {string} componentId - 要使用的组件的ID
         * @property {*} [defaultVal] - 默认值
         */
        formConfig: {
            type: Array,
            default: () => []
        },
        rules: {
            type: Object,
            default: () => {}
        },
        labelWidth: {
            type: String,
            default: ''
        },
        inline: {
            type: Boolean,
            default: false
        }
    },
    // 组件内部状态
    data() {
        return {
            COMPONENT_MAP: {
                1001: 'RamsInput',
                1002: 'RamsSelect',
                1003: 'RamsSingleDate',
                1004: 'RamsRangeDate',
                1005: 'RamsSingleTime',
                1006: 'RamsRangeTime',
                1007: 'RamsSwitch',
                1008: 'RamsReSelect',
                1099: 'Custom'
            },
            form: {},
            initForm: {},
            show: true,
            gap: 4
        };
    },
    // 监听表单数据变化
    watch: {
        form: {
            handler(newVal) {
                this.$emit('update:form', newVal);
            },
            deep: true
        }
    },
    mounted() {
        this.initFormDefValue();
    },
    beforeDestroy() {
        // 提前销毁，防止报错
        this.show = false;
    },
    components: FormItem,
    methods: {
        /**
         * 初始化表单默认值
         * - 检查key是否重复
         * - 设置默认值
         * - 更新表单数据
         */
        initFormDefValue() {
            let keyCollection = new Set();
            console.log('index', this.formConfig);
            this.formConfig.forEach((item) => {
                if (!keyCollection.has(item.key)) {
                    keyCollection.add(item.key);

                    let defaultVal = '';
                    if (![undefined, null].includes(item.defaultVal)) {
                        defaultVal = item.defaultVal;
                    }
                    // 添加此属性可不插入form中，一般button都需要这个
                    this.$set(this.form, item.key, defaultVal);
                } else {
                    throw new Error(`formData中存在重复key字段: ${item.key}`);
                }
            });
            this.$emit('update:form', this.dealFormEmptyVal(this.form)); // 立即更新表单内容
            this.initForm = _.cloneDeep(this.form);
            this.$emit('initDefaultSearch', this.initForm);
        },
        /**
         * 处理表单空值，将空字符串转换为null，处理数字类型转换
         * @param {Object} form - 表单数据对象
         * @returns {Object} 处理后的表单数据
         */
        dealFormEmptyVal(form) {
            let obj = {};
            for (let key in form) {
                let type = 'string';
                let config = this.formConfig.filter((item) => item.key == key);
                if (config.length > 0) {
                    type = config[0].type || 'string';
                }
                if ([''].includes(form[key])) {
                    // 空字符串转为 null
                    obj[key] = null;
                } else {
                    // 为number类型的转化
                    obj[key] = ['number', 'Number'].includes(type)
                        ? parseFloat(form[key])
                        : form[key];
                }
            }
            return obj;
        },
        /**
         * 处理表单项点击事件
         * @param {Object} componentInfo - 组件配置信息
         * @param {Event} event - 点击事件对象
         */
        click(componentInfo, event) {
            this.$emit(`${componentInfo.key}Click`, {
                ...componentInfo,
                event,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        /**
         * 处理表单项输入事件
         * @param {Object} componentInfo - 组件配置信息
         * @param {*} val - 输入值
         */
        input(componentInfo, val) {
            this.$emit(`${componentInfo.key}Input`, {
                ...componentInfo,
                val,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        /**
         * 处理表单项值变更事件
         * @param {Object} componentInfo - 组件配置信息
         * @param {*} val - 变更后的值
         */
        change(componentInfo, val) {
            let newParams = JSON.parse(JSON.stringify(componentInfo));
            this.$emit(`change`, { ...newParams, val });
            this.$emit(`${componentInfo.key}Change`, {
                ...newParams,
                val,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        blur(componentInfo, val) {
            this.$emit(`blur`, { ...componentInfo, val });
            this.$emit(`${componentInfo.key}Blur`, {
                ...componentInfo,
                val,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        mousedown(componentInfo) {
            this.$emit(`${componentInfo.key}Mousedown`, {
                ...componentInfo,
                val,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        focusTarget(componentInfo, val) {
            this.$emit(`focusTarget`, {
                ...componentInfo,
                val,
                form: this.dealFormEmptyVal(this.form)
            });
        },
        remoteMethod(componentInfo, value) {
            this.$emit(`remoteMethod`, componentInfo, value);
        },
        /**
         * 批量更新表单项的值
         * @param {Array<{key: string, value: *}>} valueArr - 要更新的值数组
         */
        changeComponentValue(valueArr) {
            valueArr.forEach((item) => {
                this.$set(this.form, item.key, item.value);
            });
        },
        /**
         * 根据key获取表单项实例
         * @param {string} key - 表单项的key
         * @returns {Vue} 表单项组件实例
         */
        getInstanceByKey(key) {
            const components = this.$refs[key];
            if (Array.isArray(components)) {
                return this.$refs[key][0];
            } else {
                return this.$refs[key];
            }
        },
        /**
         * 获取处理后的表单数据
         * @returns {Object} 处理后的表单数据对象
         */
        getFormData() {
            console.log('2222', this.form);
            return this.dealFormEmptyVal(this.form);
        },
        /**
         * 获取el-form实例
         * @returns {Vue} el-form组件实例
         */
        getInstance() {
            return this.$refs.form;
        },
        /**
         * 重置表单默认值
         * @param {String | Array | undefined} key - 传入的必须是表单字段key 或者 空
         */
        reset(key) {
            if (typeof key === 'string') {
                this.form[key] = this.initForm[key];
            } else if (Array.isArray(key)) {
                key.forEach((k) => {
                    this.form[k] = this.initForm[k];
                });
            } else {
                Object.keys(this.form).forEach((k) => {
                    this.form[k] = this.initForm[k];
                });
            }
        },
        /**
         * 检测表单值
         * @param {}  -
         * @returns {}
         */
        validate() {
            return new Promise((resolve, reject) => {
                this.$refs.form.validate((valid, object) => {
                    if (valid) {
                        resolve(true);
                    } else {
                        //  检测没通过，处理
                        // const keys = Object.keys(object).join(',')
                        // systemUtil.popupMessage('提示', `请完善表单内容：${keys}`)
                        reject(object);
                    }
                });
            });
        }
    }
};
</script>
<!-- 组件样式 -->
<style lang="less" scoped>
.el-form {
    &--inline {
        display: flex;
        flex-wrap: wrap;
    }
    .el-form-item {
        margin: 0px;
        display: flex;
        // flex: 0 0 33%; /* 使用 auto 来动态调整宽度 */

        /deep/ .el-form-item__label {
            flex-shrink: 0; // 防止标签缩小
            color: #92b9e5;
            font-family:
                Source Han Sans CN,
                Source Han Sans CN;
            font-weight: 400;
        }

        /deep/ .el-form-item__content {
            flex: 1; // 占据剩余空间
            margin-left: 0 !important; // 覆盖element-ui的默认margin

            // 让所有表单控件占满整个content区域
            .el-input,
            .el-select,
            .el-date-editor,
            .el-time-picker,
            .el-switch,
            .el-checkbox-group {
                width: 100%;
            }
        }

        .el-form-item__error {
            top: 80%;
        }
    }
}
</style>
