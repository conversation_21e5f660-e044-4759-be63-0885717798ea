<template>
    <div class="content-container">
        <mtv-gis :ref="gisId" :totaloptions="gisOptions" @onLoad="gisOnLoad"></mtv-gis>
    </div>
</template>

<script>
export default {
    props: {
        resultData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        resultData: {
            handler(newVal) {
                // 点/线   --数据格式
                const pointData = {
                    type: 'point',
                    list: [
                        {
                            lng: 117.1140042,
                            lat: 36.6507007,
                            // 需要展示的点键值对
                            当前道路人流量: 1592,
                            道路通行状态: '正常通行'
                        },
                        {
                            lng: 117.1240042,
                            lat: 36.6607007,
                            当前道路人流量: 5822,
                            道路通行状态: '拥堵'
                        }
                    ]
                };
                const lineData = {
                    type: 'line',
                    list: [
                        // 一个对象代表一个线段
                        {
                            endLatLng: '35.146,118.24818',
                            startLatLng: '35.16407,118.2458'
                        },
                        {
                            endLatLng: '34.80645,118.21003',
                            startLatLng: '34.8067,118.21002'
                        },
                        {
                            endLatLng: '34.76396,118.21217',
                            startLatLng: '34.80645,118.21003'
                        },
                        {
                            endLatLng: '34.82948,118.20863',
                            startLatLng: '34.80667,118.21016'
                        }
                    ]
                };
                newVal = [pointData, lineData];
                console.log(newVal);

                this.getChartData(newVal);
            },
            deep: true
        }
    },
    data() {
        return {
            gisId: 'gisId',
            gisOptions: {
                city: null,
                initialZoom: 17,
                options: {
                    antialias: true,
                    mapLayer: {
                        visible: true,
                        type: 22,
                        mapType: 'tx',
                        colorControl: false
                    },
                    cameraControl: {
                        type: '3D',
                        typeButton: true,
                        wheelLevelChange: true,
                        wheelState: true,
                        moveState: true,
                        rotateState: true,
                        minZoom: 4,
                        maxZoom: 17
                    },
                    colorLegend: {
                        visible: false,
                        autoUpdateLegend: true,
                        defaultOption: 'RSRP均值'
                    },
                    layerControl: {
                        visible: false
                    },
                    tool: {
                        visible: false
                    },

                    showInfo: {
                        visible: true,
                        float: true
                    }
                }
            },
            dateActive: 0,
            dates: ['02.18', '02.19', '02.20', '02.21', '02.22', '02.23', '02.24'],
            times: ['0', '1', '2', '3', '4', '5', '6'],
            timeActive: 0
        };
    },
    methods: {
        gisOnLoad() {},
        chooseDate(date) {
            this.dateActive = date;
        },
        chooseTime(time) {
            this.timeActive = time;
        }
    }
};
</script>

<style lang="less" scoped>
.content-container {
    width: 100%;
    height: 55vh;
    .mtv-gis {
        width: 100%;
        height: 100%;
        margin: 1.11rem 0;
        border-radius: 0.22rem;
        overflow: hidden;
    }
}
</style>
