<template>
    <el-dialog
        :visible="dialogVisible"
        :title="info.cardName"
        :modal="false"
        width="50%"
        @close="handleClose"
    >
        <div class="dialog-content">
            <div class="filter-section">
                <SearchBar
                    v-if="isConfigLoaded"
                    ref="searchBar"
                    :form-config="searchFormConfig"
                    :label-width="'80px'"
                    @search="handleSearchSubmit"
                    @reset="handleSearchReset"
                    @change="handleSearchChange"
                    @remoteSearch="handleRemoteSearch"
                />
            </div>

            <div class="sub-module_content">
                <component
                    :is="info.renderType"
                    :resultData="searchResultData"
                    v-loading="loading"
                    element-loading-text="拼命加载中"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="rgba(1,34,73, 0.6)"
                />
            </div>
        </div>
    </el-dialog>
</template>

<script>
export default {
    components: {
        SearchBar: () => import(/* webpackChunkName: "SearchBar" */ '../SearchBar/index.vue'),
        mapTimeAxis: () => import(/* webpackChunkName: "mapTimeAxis" */ './mapTimeAxis.vue'),
        multiScaleDiagram: () =>
            import(/* webpackChunkName: "multiScaleDiagram" */ './multiScaleDiagram.vue'),
        singleScaleDiagram: () =>
            import(/* webpackChunkName: "singleScaleDiagram" */ './singleScaleDiagram.vue'),
        lineChart: () => import(/* webpackChunkName: "lineChart" */ './lineChart.vue'),
        tableProportion: () =>
            import(/* webpackChunkName: "tableProportion" */ './tableProportion.vue'),
        tableValues: () => import(/* webpackChunkName: "tableValues" */ './tableValues.vue'),
        mapPointLine: () => import(/* webpackChunkName: "mapPointLine" */ './mapPointLine.vue')
    },
    name: 'DetailDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        info: {
            type: Object,
            default: () => {}
        }
    },
    mounted() {
        this.getSearchFormConfig();
    },
    data() {
        return {
            dialogVisible: false,
            searchFormConfig: [],
            searchResultData: null,
            isConfigLoaded: false, // 添加加载状态标志
            loading: false
        };
    },
    watch: {
        // 修改 watch 的写法，添加立即执行和深度监听
        visible: {
            handler(newVal) {
                this.dialogVisible = newVal;
            },
            immediate: true // 添加这个
        }
    },
    methods: {
        async getSearchFormConfig() {
            try {
                let { data } = await $request(
                    'post',
                    'mtexapi/region-service/common/card/configs',
                    {
                        cardId: this.info.cardId
                    }
                );
                this.searchFormConfig = data;
                this.isConfigLoaded = true; // 数据加载完成后设置标志
            } catch (error) {
                this.$message.error('加载表单配置失败');
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        gisOnLoad() {
            // 初始化地图，这里需要使用具体的地图API（如高德地图、百度地图等）
        },
        handleTimeChange(value) {
            // 处理时间轴变化
            console.log('当前时间点：', value);
        },

        async handleSearchSubmit(formData) {
            this.loading = true;
            // 处理搜索表单提交逻辑
            this.searchResultData = [];
            console.log('搜索参数:', formData);
            console.log(this.info);
            let { data } = await $request('post', 'mtexapi/region-service/dic/commonSelect', {
                ...formData,
                id: this.info.sqlId
            });
            this.searchResultData = data;
            this.loading = false;
        },

        handleSearchReset() {
            // 处理搜索表单重置逻辑
            console.log('表单已重置');
        },

        handleSearchChange(data) {
            // 处理表单项变更事件
            console.log('表单项变更:', data);
            // 这里可以添加额外的处理逻辑，比如更新其他相关数据
        },

        handleRemoteSearch({ componentInfo, value }) {
            // 处理远程搜索
            console.log('远程搜索:', componentInfo.key, value);
        }
    }
};
</script>

<style lang="less" scoped>
.dialog-content {
    .filter-section {
        margin-bottom: 1.11rem;
        padding: 0 10px;
    }

    .sub-module_content {
        width: 100%;
        min-height: 55vh;
        height: fit-content;
        // background: #f5f7fa;
        color: #909399;
        font-size: 0.78rem;
    }
}
</style>
